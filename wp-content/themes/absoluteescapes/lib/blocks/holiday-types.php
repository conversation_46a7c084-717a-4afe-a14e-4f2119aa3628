<?php

/**
 * Holiday Types
 */

$taxonomies = get_sub_field('taxonomies');

?>

<?php if($taxonomies) : $i = 0; ?>
<section class="holiday-types">
    <div class="holiday-types__inner" data-aos="fade">
        <div class="container holiday-types__container">
            <div class="row holiday-types__row">
                <?php foreach($taxonomies as $taxonomy) : ?>
                    <?php
                    $image_id = get_field('holiday_type_listing_image', $taxonomy);
                    $heading = $taxonomy->name;
                    $description = $taxonomy->description;
                    $image = wp_get_attachment_image($image_id, 'holiday_type_small');
                    if($i < 2) {
                        $image = wp_get_attachment_image($image_id, 'holiday_type_large');
                    }


                    ?>

                    <div class="col-md-6 holiday-types__col">
                        <div class="holiday-types__content">
                            <a href="<?php echo get_term_link($taxonomy->term_id); ?>" class="holiday-types__link">
                                <div class="row holiday-types__content-row">
                                    <div class="col-md-6 holiday-types__content-col">
                                        <div class="holiday-types__image-wrapper">
                                            <?php if($image) : ?>
                                                <div class="holiday-types__image" data-att="<?= $image_id; ?>">
                                                    <?php echo $image; ?>
                                                    <?php if($heading) : ?>
                                                        <div class="holiday-types__image-overlay">
                                                            <h3 class="holiday-types__heading"><?php echo $heading; ?></h3>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6 holiday-types__content-col">
                                        <div class="holiday-types__text">
                                            <?php if($description) : ?>
                                                <div class="holiday-types__copy">
                                                    <?php echo apply_filters('the_content', $description); ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="holiday-types__link-wrapper">
                                                <span class="holiday-types__link-text link"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span> <i class="fas fa-chevron-down"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                <?php $i++; endforeach; ?>
            </div>
        </div>
    </div>
</section><!-- .holiday-types -->
<?php endif; ?>
