<?php

/* layout blocks */

if (have_rows('page_builder')) :
	while (have_rows('page_builder')) : the_row();

if (get_row_layout() == 'banner') :
	get_template_part('lib/blocks/banner');

elseif (get_row_layout() == 'page_header') :
	get_template_part('lib/blocks/page-header');

elseif (get_row_layout() == 'heading_content') :
	get_template_part('lib/blocks/heading-content');

elseif (get_row_layout() == 'quote') :
	get_template_part('lib/blocks/quote');

elseif (get_row_layout() == 'holiday_types') :
	get_template_part('lib/blocks/holiday-types');

elseif (get_row_layout() == 'reviews') :
	get_template_part('lib/blocks/reviews');

elseif (get_row_layout() == 'favourite_holidays') :
	get_template_part('lib/blocks/favourite-holidays');

elseif (get_row_layout() == 'destinations') :
	get_template_part('lib/blocks/destinations');

elseif (get_row_layout() == 'inspiration') :
	get_template_part('lib/blocks/inspiration');

elseif (get_row_layout() == 'instagram') :
	get_template_part('lib/blocks/instagram');
	
elseif (get_row_layout() == 'steps_ahead') :
	get_template_part('lib/blocks/steps-ahead');
		
elseif (get_row_layout() == 'wysiwyg') :
	get_template_part('lib/blocks/wysiwyg');

elseif (get_row_layout() == 'carousel') :
	get_template_part('lib/blocks/carousel');

elseif (get_row_layout() == 'holidays') :
	get_template_part('lib/blocks/holidays');

elseif (get_row_layout() == 'image') :
	get_template_part('lib/blocks/image');

elseif (get_row_layout() == 'image_content_columns') :
	get_template_part('lib/blocks/image-content-columns');	

elseif (get_row_layout() == 'map') :
	get_template_part('lib/blocks/map');	

elseif (get_row_layout() == 'we_recommend') :
	get_template_part('lib/blocks/we-recommend');

elseif (get_row_layout() == 'form') :
	get_template_part('lib/blocks/form');

elseif (get_row_layout() == 'contact') :
	get_template_part('lib/blocks/contact');

elseif (get_row_layout() == 'holiday_listing') :
	get_template_part('lib/blocks/holiday-listing');

elseif (get_row_layout() == 'cta') :
	get_template_part('lib/blocks/cta');

endif;

endwhile;

else :

	get_template_part('lib/blocks/no-content');

endif;

?>
