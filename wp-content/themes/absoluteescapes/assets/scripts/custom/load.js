$(window).on('load', function () {


    var $els = $(".subnav");

    if ($els.length) {
        Stickyfill.add($els);
    }

    var $enquiryForm = $('.enquiry-form');

    if ($enquiryForm.length) {
        Stickyfill.add($enquiryForm);
    }

    var getUrlParameter = function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
    };

    var mastheadHeight = 115;

    function isAnyPartOfElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        // DOMRect { x: 8, y: 8, width: 100, height: 100, top: 8, right: 108, bottom: 108, left: 8 }
        var windowHeight =
            window.innerHeight || document.documentElement.clientHeight;
        var windowWidth =
            window.innerWidth || document.documentElement.clientWidth;

        // http://stackoverflow.com/questions/325933/determine-whether-two-date-ranges-overlap
        var vertInView =
            rect.top <= windowHeight &&
            rect.top +
            (rect.height - (mastheadHeight + 1)) >=
            0;
        var horInView =
            rect.left <= windowWidth && rect.left + rect.width >= 0;

        return vertInView && horInView;

    }


    $(document).on('click', '.masthead a', function (e) {
        if (/#/.test(this.href) && !$(this).hasClass('aito-link') && !$(this).hasClass('modal-close')) {
            //if ($(this).is('[href*="#"]')) {
            e.preventDefault();
        }
    });

    $('.aito-link').on('click', function () {

        $('.review-bar').addClass('active');

    });

    $('.modal-close').on('click', function () {

        $('.review-bar').removeClass('active');

    });

    var $scrollNext = $('.scroll-next');

    $scrollNext.on('click', function () {
        $('html, body').animate({
            scrollTop: $(this).closest('section').next().offset().top - 50
        }, 500);
    });

var $scrollTo = $('.scroll-to');

$scrollTo.on('click', function () {
    // Get the target ID from data attribute
    var targetId = $(this).data('target');

    // If a target ID is specified, scroll to that element
    if (targetId) {
        var $targetElement = $('#' + targetId);
        if ($targetElement.length) {
            $('html, body').animate({
                scrollTop: $targetElement.offset().top - 50
            }, 500);
        }
    }
});



    var $backToTop = $('.back-to-top');

    $backToTop.on('click', function () {
        $('html, body').animate({
            scrollTop: $('html').offset().top
        }, 1000);
    });


    var $reviews = $('.reviews__row');

    $reviews.on('ready.flickity', function () {

        $(this).parent().find('.reviews__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.reviews__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $reviews.flickity({
        wrapAround: true,
        contain: true,
        freeScroll: true
    });


    // Function to wrap title text if 3 or more words
    function wrapFavouriteHolidaysTitles() {
        $('.favourite-holidays__title').each(function() {
            var $title = $(this);

            // Store original text and HTML to avoid repeated processing
            if (!$title.data('original-text')) {
                $title.data('original-text', $title.text().trim());
                $title.data('original-html', $title.html().trim());
            }

            var originalText = $title.data('original-text');
            var originalHtml = $title.data('original-html');

            // Reset to original content first to get accurate measurements
            $title.html(originalHtml);

            // Check if text is already on multiple lines by checking:
            // 1. Original HTML contains <br> tags
            // 2. Original text contains line breaks
            // 3. Element height suggests multiple lines (browser natural wrapping)
            var hasExplicitLineBreaks = originalHtml.includes('<br>') ||
                                       originalHtml.includes('<BR>') ||
                                       originalText.includes('\n') ||
                                       originalText.includes('\r');

            if (hasExplicitLineBreaks) {
                // Text has explicit line breaks, don't apply wrap rule
                return;
            }

            // Check for natural browser wrapping by measuring height
            var currentHeight = $title.height();
            var lineHeight = parseFloat($title.css('line-height'));
            if (!lineHeight || lineHeight === 0) {
                lineHeight = parseFloat($title.css('font-size')) * 1.2;
            }
            var isNaturallyWrapped = currentHeight > (lineHeight * 1.4); // Tighter tolerance

            if (isNaturallyWrapped) {
                // Text is naturally wrapped by browser
                // Check if there's only one word on the second line by testing different break points
                var words = originalText.split(/\s+/);
                if (words.length >= 3) {
                    // Create a test element with same styling to measure text width
                    var $testElement = $title.clone();
                    $testElement.css({
                        'position': 'absolute',
                        'visibility': 'hidden',
                        'white-space': 'nowrap',
                        'width': 'auto',
                        'height': 'auto'
                    });
                    $testElement.appendTo($title.parent());

                    // Test if all words except the last one fit on one line
                    var testFirstLine = words.slice(0, -1).join(' ');
                    $testElement.html(testFirstLine);
                    var testWidth = $testElement.width();
                    var containerWidth = $title.width();

                    // Clean up test element
                    $testElement.remove();

                    // If the first line (without last word) fits in container,
                    // it means the last word was alone on second line
                    if (testWidth <= containerWidth) {
                        // Only one word on second line, so keep last 2 words together
                        var firstLine = words.slice(0, -2).join(' ');
                        var secondLine = words.slice(-2).join(' ');
                        var wrappedText = firstLine + '<br>' + secondLine;
                        $title.html(wrappedText);
                    } else {
                        // Multiple words on second line, leave natural wrapping
                        $title.html(originalHtml);
                    }
                } else {
                    // Less than 3 words, restore original
                    $title.html(originalHtml);
                }
                return;
            }

            var words = originalText.split(/\s+/); // Split on any whitespace and remove empty strings

            if (words.length >= 3) {
                var firstLine, secondLine;

                // Check if any of the first words (excluding the last word) are less than 4 characters
                var hasShortWords = false;
                for (var i = 0; i < words.length - 1; i++) {
                    if (words[i].length < 4) {
                        hasShortWords = true;
                        break;
                    }
                }

                if (hasShortWords) {
                    // If there are short words, wrap only the last word to the second line
                    firstLine = words.slice(0, -1).join(' ');
                    secondLine = words.slice(-1).join(' ');
                } else {
                    // Default behavior: wrap the last 2 words to the second line
                    firstLine = words.slice(0, -2).join(' ');
                    secondLine = words.slice(-2).join(' ');
                }

                // Create the wrapped HTML
                var wrappedText = firstLine + '<br>' + secondLine;
                $title.html(wrappedText);
            }
        });
    }

    // Function to equalize heights of favourite holidays content
    function equalizeFavouriteHolidaysHeights() {
        var $contents = $('.favourite-holidays__col-content');
        var $backgrounds = $('.favourite-holidays__background');
        var maxContentHeight = 0;

        // Reset heights first
        $backgrounds.css('height', '');
        $contents.css('min-height', '');

        // Find the tallest content
        $contents.each(function() {
            var contentHeight = $(this).outerHeight();
            if (contentHeight > maxContentHeight) {
                maxContentHeight = contentHeight;
            }
        });

        // Set minimum height for all content elements to match the tallest
        $contents.css('min-height', maxContentHeight + 'px');

        // Also ensure backgrounds have proper height
        var minBackgroundHeight = 445; // Minimum height from CSS
        var totalBackgroundHeight = Math.max(maxContentHeight + 40, minBackgroundHeight); // 40px for padding
        $backgrounds.css('height', totalBackgroundHeight + 'px');
    }

    var $favHolidays = $('.favourite-holidays__row');

    $favHolidays.on('ready.flickity', function () {

        $(this).parent().find('.favourite-holidays__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.favourite-holidays__button--next').appendTo($(this).find('.flickity-button.next'));

        // Wrap titles and equalize heights after slider is ready
        setTimeout(function() {
            wrapFavouriteHolidaysTitles();
            equalizeFavouriteHolidaysHeights();
        }, 100);

    });

    // Also apply wrapping and height equalization when slider changes
    $favHolidays.on('change.flickity', function() {
        setTimeout(function() {
            wrapFavouriteHolidaysTitles();
            equalizeFavouriteHolidaysHeights();
        }, 50);
    });

    $favHolidays.flickity({
        freeScroll: true,
        groupCells: true
    });

    // Debounce function to limit resize handler calls
    function debounce(func, wait) {
        var timeout;
        return function executedFunction() {
            var context = this;
            var args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Debounced function for resize handling
    var debouncedFavouriteHolidaysResize = debounce(function() {
        if ($('.favourite-holidays__row').length) {
            wrapFavouriteHolidaysTitles();
            equalizeFavouriteHolidaysHeights();
        }
    }, 250);

    // Also wrap titles and equalize heights on window resize (debounced)
    $(window).on('resize', debouncedFavouriteHolidaysResize);


    var $destinations = $('.destinations__row');

    $destinations.on('ready.flickity', function () {

        $(this).parent().find('.favourite-holidays__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.favourite-holidays__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $destinations.flickity({
        wrapAround: true,
        pageDots: false,
        freeScroll: true,
        contain: true,
        groupCells: true,
        cellAlign: 'left'
    });


    var $photos = $('.instagram__photos');

    $photos.on('ready.flickity', function () {

        $(this).parent().find('.instagram__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.instagram__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $photos.flickity({
        pageDots: false,
        freeScroll: true,
        contain: true,
        groupCells: true,
        cellAlign: 'left'
    });

    var $carousel = $('.carousel__images');

    $carousel.on('ready.flickity', function () {

        $(this).parent().find('.carousel__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.carousel__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $carousel.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
    });

    var $holidaysGallery = $('.holidays__gallery');

    $holidaysGallery.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
        arrowShape: {
            x0: 10,
            x1: 60, y1: 50,
            x2: 70, y2: 35,
            x3: 35
        }
    });


    var $accommodationGallery = $('.accommodation__images');

    $accommodationGallery.on('ready.flickity', function () {

        $(this).parent().find('.accommodation__button--prev').appendTo($(this).find('.flickity-button.previous'));
        $(this).parent().find('.accommodation__button--next').appendTo($(this).find('.flickity-button.next'));

    });

    $accommodationGallery.flickity({
        wrapAround: true,
        contain: true,
        groupCells: true,
    });


    var $grid = $(".inf-posts");

    if ($('.next-posts-link').length && $grid.length) {
        $grid.infiniteScroll({
            path: ".next-posts-link a",
            append: ".inf-posts .inf-post",
            history: true,
            button: ".button-inf",
            scrollThreshold: false,
            status: ".page-load-status"
        });

        $grid.on("append.infiniteScroll", function (
            event,
            response,
            path,
            items
        ) {
            $(items).addClass("appended-item");
            $grid.imagesLoaded(function () {

                $(items)
                    .find("img")
                    .each(function (index, img) {
                        img.outerHTML = img.outerHTML;
                    });

                if ($(document).find('.holidays__gallery').length) {
                    $(document).find('.holidays__gallery').flickity({
                        wrapAround: true,
                        contain: true,
                        groupCells: true,
                        arrowShape: {
                            x0: 10,
                            x1: 60, y1: 50,
                            x2: 70, y2: 35,
                            x3: 35
                        }
                    });
                }
            });
        });
    }


    var rangeSlider = document.getElementById('distanceRangeSlider');

    if ($('#distanceRangeSlider').length) {
        noUiSlider.create(rangeSlider, {
            start: [1, 32],
            connect: true,
            step: 1,
            range: {
                'min': 1,
                'max': 32
            },
        });


        var minDuration = getUrlParameter('durationmin');
        var maxDuration = getUrlParameter('durationmax');

        if (minDuration && maxDuration) {
            rangeSlider.noUiSlider.set([minDuration, maxDuration]);
        } else if (minDuration) {
            rangeSlider.noUiSlider.set([minDuration, null]);
        } else if (maxDuration) {
            rangeSlider.noUiSlider.set([null, maxDuration]);
        }


        rangeSlider.noUiSlider.on('update', function (values, handle) {
            $('.filter__range-number--min').text(Math.floor(values[0]));
            $('#durationMin').val(Math.floor(values[0]));
            $('.filter__range-number--max').text(Math.floor(values[1]));
            $('#durationMax').val(Math.floor(values[1]));
        });

        $('#orderDropdown').on('change', function () {
            $('#sort').val($(this).val());
            $('#filterForm').submit();
        });

        rangeSlider.noUiSlider.on('change', function () {
            $('#filterForm').submit();
        });

    }

    $('.filter__input').on('change', function () {
        $('#filterForm').submit();
    });

    var dropdownMoving = false;

    $('.filter__label-wrapper').on('click', function () {

        var $this = $(this);

        if (!dropdownMoving) {
            dropdownMoving = true;

            if (!$this.hasClass('collapsed')) {
                $this.next().slideUp(function () {
                    $this.addClass('collapsed');
                    dropdownMoving = false;
                });
            } else {
                $this.next().slideDown(function () {
                    $this.removeClass('collapsed');
                    dropdownMoving = false;
                });

            }
        }
    });


    var images = [];
    var galleryObj;

    $('.page-header__gallery').find('img').each(function () {
        galleryObj = {}
        galleryObj['src'] = $(this).attr('src');
        images.push(galleryObj);

    });

    $('#galleryTrigger').on('click', function (e) {
        e.preventDefault();

        $.fancybox.open(images, {
            loop: true
        });
        $('[data-fancybox="gallery"]').fancybox({
          afterLoad : function(instance, current) {
            current.$image.attr('alt', current.opts.$orig.find('img').attr('alt') );
          }
        });
    });

    var overAccordionAnimating = false;

    $('.accordion__heading-wrapper').on('click', function () {
        var $this = $(this);

        if (!overAccordionAnimating) {
            overAccordionAnimating = true;

            if (!$this.parent().hasClass('active')) {
                $this.parent().addClass('active');
                $this.next().slideDown(function () {
                    overAccordionAnimating = false;
                });
            } else {
                $this.parent().removeClass('active');
                $this.next().slideUp(function () {
                    overAccordionAnimating = false;
                });
            }
        }

    });

    $('.accordion__close').on('click', function () {

        if (!overAccordionAnimating) {
            overAccordionAnimating = true;

            var $this = $(this);

            $this.parent().parent().slideUp(function () {
                overAccordionAnimating = false;

                $this.parent().parent().parent().removeClass('active');
            });

        }

    });

    // $('.subnav__link').on('click', function() {
    //     $('.subnav__item').removeClass('active');

    //     $(this).parent().addClass('active');

    // });

    $('#subNav').on('change', function () {
        var value = $(this).val();

        $('html, body').animate({
            scrollTop: $('#' + value).offset().top - 100
        }, 0);
    });


    if ($(".subnav").length) {
        setTimeout(function () {
            if ($(this).scrollTop() <= 150) {
                $('.subnav__link').parent().removeClass('active');
                $('#subNav').val('');
            } else {
                $("section").each(function () {
                    if ($(this).prop("id") !== "") {
                        var $that = $(this);
                        if (isAnyPartOfElementInViewport(this)) {
                            $(".subnav__link").each(function () {
                                if ($(this).attr("data-id") === $that.prop("id")) {
                                    $(
                                        ".subnav__link[data-id=" +
                                        $that.prop("id") +
                                        "]"
                                    )
                                        .parent()
                                        .addClass("active");

                                    $('#subNav').val($that.prop("id"));
                                } else {
                                    $(".subnav__link")
                                        .not(
                                            ".subnav__link[data-id=" +
                                            $that.prop("id") +
                                            "]"
                                        )
                                        .parent()
                                        .removeClass("active");

                                    $('#subNav').val('');
                                }
                            });

                            return false;
                        } else {
                            $(".subnav__link")
                                .not(
                                    ".subnav__link[data-id=" +
                                    $that.prop("id") +
                                    "]"
                                )
                                .parent()
                                .removeClass("active");

                            $('#subNav').val('');
                        }
                    }
                });
            }
        }, 50);


        $(window).on("scroll", function () {
            if ($(this).scrollTop() <= 150) {
                $('.subnav__link').parent().removeClass('active');
                $('#subNav').val('');
            } else {
                $("section").each(function () {
                    if ($(this).prop("id") !== "") {
                        var $that = $(this);
                        if (isAnyPartOfElementInViewport(this)) {
                            $(".subnav__link").each(function () {
                                if ($(this).attr("data-id") === $that.prop("id")) {
                                    $(
                                        ".subnav__link[data-id=" +
                                        $that.prop("id") +
                                        "]"
                                    )
                                        .parent()
                                        .addClass("active");

                                    $('#subNav').val($that.prop('id'));
                                } else {
                                    $(".subnav__link")
                                        .not(
                                            ".subnav__link[data-id=" +
                                            $that.prop("id") +
                                            "]"
                                        )
                                        .parent()
                                        .removeClass("active");


                                }
                            });

                            return false;
                        } else {
                            $(".subnav__link")
                                .not(
                                    ".subnav__link[data-id=" +
                                    $that.prop("id") +
                                    "]"
                                )
                                .parent()
                                .removeClass("active");

                            $('#subNav').val('');
                        }
                    }
                });
            }
        });
    }

    $('[data-toggle="datepicker"]').datepicker({
        format: 'dd/mm/yyyy',
        autoHide: true
    });


    $('.tooltip').on('click', function () {

        $(this).toggleClass('active');

    });

    $('.search-trigger').on('click', function () {

        $('.masthead__form-container').toggleClass('active');

        if ($('.masthead__form-container').hasClass('active')) {
            $('.masthead__form > input').focus();
        }

    });

    $('.masthead__form-close').on('click', function () {

        $('.masthead__form-container').removeClass('active');

    });

    var overviewCopyAnimating = false;

    $('.overview__bottom-copy-trigger').on('click', function () {

        if(!overviewCopyAnimating) {
            overviewCopyAnimating = true;
            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                $(this).next().slideDown(function () {
                    overviewCopyAnimating = false;
                });
            } else {
                $(this).removeClass('active');
                $(this).next().slideUp(function () {
                    overviewCopyAnimating = false;
                });
            }
        }

    });

    if($('.banner__slideshow').length) {
        $('.banner__slideshow').slideshow({
            randomize: false,            // Randomize the play order of the slides.
            slideDuration: 6000,        // Duration of each induvidual slide.
            fadeDuration: 1000,         // Duration of the fading transition. Should be shorter than slideDuration.
            animate: true,              // Turn css animations on or off.
            pauseOnTabBlur: true,

        });
    }

    AOS.init({
        once: true
    });

    $('.overlay').fadeOut();

});
