/* ==========================================================================

   1. Reset
   2. Grid
   3. Base styles: opinionated defaults
   4. Variables
   5. Mixins
   6. Fonts
   7. Plugins
   8. Custom styles
   9. Browser Fixes
   10. Responsive

   */

/* ==========================================================================
   1. Reset
   ========================================================================== */

@import "normalize";

/* ==========================================================================
   2. Grid
   ========================================================================== */

@import "bootstrapcustom";

/* ==========================================================================
   3. Base Styles
   ========================================================================== */

@import "base";

/* ==========================================================================
   4. Variables
   ========================================================================== */

@import "variables";

/* ==========================================================================
   5. Mixins
   ========================================================================== */

@import "mixins";

/* ==========================================================================
   6. Fonts
   ========================================================================== */

@import "fonts";

/* ==========================================================================
   7. Plugins
   ========================================================================== */

@import "vendor/aos";
@import "vendor/fancybox";
@import "vendor/flickity";
@import "vendor/nouislider";
@import "vendor/datepicker";
@import "vendor/subtle-slideshow";

/* ==========================================================================
   8. Custom Styles
   ========================================================================== */

@import "general-styles";

//Structure
@import "structure/header";
@import "structure/footer";

//Components
@import "components/burger";
@import "components/form";
@import "components/categories";
@import "components/breadcrumbs";
@import "components/share";
@import "components/filter";
@import "components/subnav";
@import "components/holiday-form";
@import "components/holidays-results";
@import "components/form-modal";

//Templates
@import "templates/archive";
@import "templates/blog-posts";
@import "templates/blog-post";
@import "templates/search";
@import "templates/taxonomy";

//Blocks
@import "blocks/banner";
@import "blocks/quote";
@import "blocks/holiday-types";
@import "blocks/reviews";
@import "blocks/favourite-holidays";
@import "blocks/destinations";
@import "blocks/inspiration";
@import "blocks/instagram";
@import "blocks/steps-ahead";
@import "blocks/carousel";
@import "blocks/wysiwyg";
@import "blocks/holidays";
@import "blocks/page-header";
@import "blocks/we-recommend";
@import "blocks/image-content-columns";
@import "blocks/map";
@import "blocks/heading-content";
@import "blocks/form";
@import "blocks/contact";
@import "blocks/cta";

//Blocks Holiday
@import "blocks/holiday/overview";
@import "blocks/holiday/route";
@import "blocks/holiday/accommodation";
@import "blocks/holiday/car-hire";
@import "blocks/holiday/itineraries-prices";
@import "blocks/holiday/package-info";
@import "blocks/holiday/travel-info";
@import "blocks/holiday/additional-days";
@import "blocks/holiday/related-posts";
@import "blocks/holiday/guided-tours";

/* ==========================================================================
   9. Browser Fixes
   ========================================================================== */

@import "browsers";

/* ==========================================================================
   10. Responsive
   ========================================================================== */

@import "responsive";
