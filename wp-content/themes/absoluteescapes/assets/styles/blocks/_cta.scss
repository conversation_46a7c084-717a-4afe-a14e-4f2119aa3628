// CTA Block Styles
// Simple curve implementation for the CTA block

.listing-cta.cta-block {
    // Override the default curve from base styles
    .listing-cta__background {
        // Default padding for no curve - balanced top/bottom
        padding: 60px 0;

        &::after {
            display: none;
        }

        // Bottom curve - override the base ::after
        &.curve-bottom {
            padding: 40px 0 100px 0;

            &::after {
                content: '';
                display: block;
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                transform: none;
                z-index: 1;
            }
        }

        // Top curve - use ::before
        &.curve-top {
            padding: 100px 0 40px 0;

            &::before {
                content: '';
                position: absolute;
                top: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                transform: scaleY(-1);
                z-index: 1;
            }
        }
    }
}
