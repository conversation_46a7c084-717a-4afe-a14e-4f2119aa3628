.instagram {

    &__inner {
        position: relative;
        padding: 50px 0;
        background: $offwhitetwo;

        // Default: no curve
        &:after {
            display: none;
        }

        // Default bottom curve (when no curve class is specified)
        &:not(.curve-top):not(.curve-bottom) {
            padding-bottom: 150px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding-bottom: 100px;
            }

            &:after {
                content: '';
                display: block;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 90px;
                background: url(../img/banner-mask.svg) center top no-repeat;
                background-size: 100% 100%;

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    height: 60px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    height: 45px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    height: 30px;
                }
            }
        }

        // Bottom curve
        &.curve-bottom {
            padding-bottom: 150px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding-bottom: 100px;
            }

            &:after {
                content: '';
                display: block;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 90px;
                background: url(../img/banner-mask.svg) center top no-repeat;
                background-size: 100% 100%;

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    height: 60px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    height: 45px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    height: 30px;
                }
            }
        }

        // Top curve
        &.curve-top {
            padding-top: 150px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding-top: 100px;
            }

            &:before {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 90px;
                background: url(../img/banner-mask.svg) center bottom no-repeat;
                background-size: 100% 100%;
                transform: scaleY(-1);

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    height: 60px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    height: 45px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    height: 30px;
                }
            }
        }
    }

    &__container {
        &--photos {
            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                padding: 0;
            }
        }
    }


    &__heading {
        margin-bottom: 45px;
    }

    &__photos-wrapper {
        padding: 60px 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 45px 0;
        }
    }

    &__photos {
        max-width: 1770px;
        margin: 0 auto;
    }

    &__photo {
        width: 20%;
        padding: 0 20px;
        text-align: center;


        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            width: 230px;
            padding: 0 15px;
        }
    }

    &__link-wrapper {
        margin: 0 25px 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            display: block;
            margin: 0 10px 15px;
        }

        .social-button {
            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                display: inline-block;
                width: 100%;
                max-width: 260px;
            }
        }
    }

    .flickity-button {
        border-color: $blue;
        background: $blue;
        color: $white;
        box-shadow: 0 0 25px rgba($black, 0.35);

        &:hover, &:focus {
            border-color: $teal;
            background: $teal;
            color: $white;
        }

        &:disabled {
            opacity: 0;
        }

        &.previous {
            left: -20px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                left: 10px;
            }

        }

        &.next {
            right: -20px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                right: 10px;
            }
        }
    }

}

.blog {
    .instagram {
        &__inner {
            padding: 50px 0 75px;
            &:after {
                display: none;
            }
        }
    }
}
